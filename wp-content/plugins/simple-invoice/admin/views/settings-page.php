<?php
/**
 * Settings Page Template
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get current settings
$settings = si_get_settings();

// Test if functions are working
error_log('Settings page loaded. Current settings: ' . print_r($settings, true));

// Add debug info to page
if (isset($_GET['debug'])) {
    echo '<div class="notice notice-info"><p><strong>DEBUG: Current $settings variable:</strong><br><pre>' . print_r($settings, true) . '</pre></p></div>';
}

// Remove the old failing test - we have better debug tools now

// Debug form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    error_log('POST request received');
    error_log('POST data: ' . print_r($_POST, true));
}

// Handle form submission
if (isset($_POST['save_settings']) && wp_verify_nonce($_POST['_wpnonce'], 'si_settings_nonce')) {
    error_log('Form submission detected - save_settings button pressed');
    error_log('Nonce verification passed');

    $updated_settings = array(
        'business_name' => si_sanitize_text($_POST['business_name'] ?? ''),
        'business_address' => si_sanitize_textarea($_POST['business_address'] ?? ''),
        'business_email' => si_sanitize_email($_POST['business_email'] ?? ''),
        'business_phone' => si_sanitize_text($_POST['business_phone'] ?? ''),
        'business_logo' => si_sanitize_url($_POST['business_logo'] ?? ''),
        'gstin' => si_sanitize_text($_POST['gstin'] ?? ''),
        'default_due_days' => intval($_POST['default_due_days'] ?? 7),
        'payment_methods' => isset($_POST['payment_methods']) && is_array($_POST['payment_methods'])
            ? array_map('si_sanitize_text', $_POST['payment_methods'])
            : array(),
        'bank_details' => si_sanitize_textarea($_POST['bank_details'] ?? ''),
        'paypal_email' => si_sanitize_email($_POST['paypal_email'] ?? ''),
        'upi_id' => si_sanitize_text($_POST['upi_id'] ?? ''),
        'footer_notes' => si_sanitize_textarea($_POST['footer_notes'] ?? ''),
        'terms_text' => si_sanitize_textarea($_POST['terms_text'] ?? ''),
        'clear_data_on_deactivation' => isset($_POST['clear_data_on_deactivation']) ? 1 : 0
    );

    error_log('Updated settings array: ' . print_r($updated_settings, true));

    // Use the working si_update_settings function
    $result = si_update_settings($updated_settings);
    error_log('si_update_settings result: ' . var_export($result, true));

    if ($result) {
        echo '<div class="notice notice-success"><p>' . __('Settings saved successfully.', 'simple-invoice') . '</p></div>';
        // Force refresh the settings variable so form shows updated values
        $settings = si_get_settings();
        error_log('Settings saved successfully. Refreshed settings: ' . print_r($settings, true));
    } else {
        echo '<div class="notice notice-error"><p>' . __('Failed to save settings.', 'simple-invoice') . '</p></div>';
        error_log('Failed to save settings');
    }
} else {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        error_log('POST received but form submission not detected');
        error_log('save_settings isset: ' . (isset($_POST['save_settings']) ? 'yes' : 'no'));
        error_log('nonce verification: ' . (wp_verify_nonce($_POST['_wpnonce'] ?? '', 'si_settings_nonce') ? 'passed' : 'failed'));
    }
}

// Handle debug save
if (isset($_POST['debug_save']) && wp_verify_nonce($_POST['_wpnonce'], 'si_settings_nonce')) {
    echo '<div class="notice notice-info"><p><strong>DEBUG SAVE TRIGGERED</strong></p></div>';
    echo '<div class="notice notice-info"><p>POST data: <pre>' . print_r($_POST, true) . '</pre></p></div>';

    // Test basic WordPress options functionality
    $test_option_name = 'si_debug_test_' . time();
    $test_option_value = array('test' => 'value', 'timestamp' => time());

    echo '<div class="notice notice-info"><p><strong>Testing WordPress options system:</strong></p></div>';

    // Test add_option
    $add_result = add_option($test_option_name, $test_option_value);
    echo '<div class="notice notice-info"><p>add_option result: ' . ($add_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    // Test get_option
    $get_result = get_option($test_option_name);
    echo '<div class="notice notice-info"><p>get_option result: <pre>' . print_r($get_result, true) . '</pre></p></div>';

    // Test update_option
    $test_option_value['updated'] = true;
    $update_result = update_option($test_option_name, $test_option_value);
    echo '<div class="notice notice-info"><p>update_option result: ' . ($update_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    // Verify update
    $verify_result = get_option($test_option_name);
    echo '<div class="notice notice-info"><p>Verified updated option: <pre>' . print_r($verify_result, true) . '</pre></p></div>';

    // Clean up test option
    delete_option($test_option_name);

    echo '<div class="notice notice-info"><p><strong>Testing si_settings specifically:</strong></p></div>';

    // Check if si_settings option exists
    $current_si_settings = get_option('si_settings');
    echo '<div class="notice notice-info"><p>Current si_settings option: <pre>' . print_r($current_si_settings, true) . '</pre></p></div>';

    // Test direct update_option on si_settings
    $test_settings = array(
        'business_name' => 'Debug Test Business',
        'business_email' => '<EMAIL>',
        'debug_timestamp' => time()
    );

    $direct_result = update_option('si_settings', $test_settings);
    echo '<div class="notice notice-info"><p>Direct update_option on si_settings result: ' . ($direct_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    // Verify direct update
    $verify_direct = get_option('si_settings');
    echo '<div class="notice notice-info"><p>Verified si_settings after direct update: <pre>' . print_r($verify_direct, true) . '</pre></p></div>';

    // Now test si_update_settings function with simple data
    echo '<div class="notice notice-info"><p><strong>Testing si_update_settings with simple data:</strong></p></div>';

    $simple_test = array(
        'business_name' => 'Simple Test Name',
        'business_email' => '<EMAIL>'
    );

    $result = si_update_settings($simple_test);
    echo '<div class="notice notice-info"><p>si_update_settings with simple data result: ' . ($result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    $verify = si_get_settings();
    echo '<div class="notice notice-info"><p>Settings after simple test: <pre>' . print_r($verify, true) . '</pre></p></div>';

    // Test with the actual form data
    echo '<div class="notice notice-info"><p><strong>Testing si_update_settings with form data:</strong></p></div>';

    $form_test = array(
        'business_name' => $_POST['business_name'] ?? 'Form Test Business',
        'business_address' => $_POST['business_address'] ?? 'Form Test Address',
        'business_email' => $_POST['business_email'] ?? '<EMAIL>',
        'business_phone' => $_POST['business_phone'] ?? '**********'
    );

    $form_result = si_update_settings($form_test);
    echo '<div class="notice notice-info"><p>si_update_settings with form data result: ' . ($form_result ? 'SUCCESS' : 'FAILED') . '</p></div>';

    $final_verify = si_get_settings();
    echo '<div class="notice notice-info"><p>Final settings after form test: <pre>' . print_r($final_verify, true) . '</pre></p></div>';

    // IMPORTANT: Update the $settings variable so the form shows the latest data
    $settings = $final_verify;
    echo '<div class="notice notice-success"><p><strong>Settings variable updated for form display!</strong></p></div>';
}

// Handle clear data submission
if (isset($_POST['clear_data_submit']) && wp_verify_nonce($_POST['_wpnonce'], 'si_clear_data_nonce')) {
    $clear_options = array();

    if (isset($_POST['clear_clients'])) $clear_options[] = 'clients';
    if (isset($_POST['clear_templates'])) $clear_options[] = 'templates';
    if (isset($_POST['clear_invoices'])) $clear_options[] = 'invoices';
    if (isset($_POST['clear_settings'])) $clear_options[] = 'settings';

    if (!empty($clear_options)) {
        $result = si_clear_plugin_data($clear_options);
        if ($result) {
            echo '<div class="notice notice-success"><p>' . __('Selected data cleared successfully.', 'simple-invoice') . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>' . __('Failed to clear some data. Please try again.', 'simple-invoice') . '</p></div>';
        }
    } else {
        echo '<div class="notice notice-warning"><p>' . __('Please select at least one data type to clear.', 'simple-invoice') . '</p></div>';
    }
}
?>

<div class="wrap si-settings-wrap">
    <h1 class="wp-heading-inline"><?php echo esc_html__('Simple Invoice Settings', 'simple-invoice'); ?></h1>
    <a href="<?php echo esc_url(admin_url('admin.php?page=simple-invoice')); ?>" class="page-title-action">
        <span class="dashicons dashicons-arrow-left-alt"></span>
        <?php echo esc_html__('Back to Dashboard', 'simple-invoice'); ?>
    </a>

    <hr class="wp-header-end">

    <!-- Settings Navigation Tabs -->
    <div class="si-settings-nav">
        <nav class="nav-tab-wrapper">
            <a href="#business-info" class="nav-tab nav-tab-active" data-tab="business-info">
                <span class="dashicons dashicons-building"></span>
                <?php echo esc_html__('Business Info', 'simple-invoice'); ?>
            </a>
            <a href="#invoice-settings" class="nav-tab" data-tab="invoice-settings">
                <span class="dashicons dashicons-media-text"></span>
                <?php echo esc_html__('Invoice Settings', 'simple-invoice'); ?>
            </a>
            <a href="#payment-settings" class="nav-tab" data-tab="payment-settings">
                <span class="dashicons dashicons-money-alt"></span>
                <?php echo esc_html__('Payment Settings', 'simple-invoice'); ?>
            </a>
            <a href="#footer-settings" class="nav-tab" data-tab="footer-settings">
                <span class="dashicons dashicons-editor-alignleft"></span>
                <?php echo esc_html__('Footer & Terms', 'simple-invoice'); ?>
            </a>
            <a href="#clear-data" class="nav-tab" data-tab="clear-data">
                <span class="dashicons dashicons-trash"></span>
                <?php echo esc_html__('Clear Data', 'simple-invoice'); ?>
            </a>
        </nav>
    </div>

    <?php if (isset($_GET['debug'])): ?>
        <div class="notice notice-info">
            <p><strong>DEBUG: Form will be populated with these values:</strong></p>
            <ul>
                <li>Business Name: "<?php echo esc_html($settings['business_name'] ?? 'EMPTY'); ?>"</li>
                <li>Business Address: "<?php echo esc_html($settings['business_address'] ?? 'EMPTY'); ?>"</li>
                <li>Business Email: "<?php echo esc_html($settings['business_email'] ?? 'EMPTY'); ?>"</li>
                <li>Business Phone: "<?php echo esc_html($settings['business_phone'] ?? 'EMPTY'); ?>"</li>
            </ul>
        </div>
    <?php endif; ?>

    <form method="post" action="" class="si-settings-form">
        <?php wp_nonce_field('si_settings_nonce'); ?>
        
        <div class="si-settings-container">

            <!-- Business Information Tab -->
            <div class="si-settings-tab si-tab-content" id="business-info">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-building"></span>
                            <?php echo esc_html__('Business Information', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Configure your business details that will appear on invoices and communications.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="business_name"><?php echo esc_html__('Business Name', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="business_name" 
                                   name="business_name" 
                                   value="<?php echo esc_attr($settings['business_name'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Enter your business name', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('This will appear on your invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_address"><?php echo esc_html__('Business Address', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="business_address" 
                                      name="business_address" 
                                      rows="4" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Enter your complete business address', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['business_address'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Your business address for invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_email"><?php echo esc_html__('Business Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email" 
                                   id="business_email" 
                                   name="business_email" 
                                   value="<?php echo esc_attr($settings['business_email'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Contact email for your business.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_phone"><?php echo esc_html__('Business Phone', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="business_phone" 
                                   name="business_phone" 
                                   value="<?php echo esc_attr($settings['business_phone'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('+****************', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Contact phone number for your business.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="business_logo"><?php echo esc_html__('Business Logo', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="url" 
                                   id="business_logo" 
                                   name="business_logo" 
                                   value="<?php echo esc_attr($settings['business_logo'] ?? ''); ?>" 
                                   class="regular-text si-media-input" 
                                   placeholder="<?php echo esc_attr__('Logo URL', 'simple-invoice'); ?>" />
                            <button type="button" class="button si-media-button" data-target="business_logo">
                                <?php echo esc_html__('Select Logo', 'simple-invoice'); ?>
                            </button>
                            <p class="description"><?php echo esc_html__('Upload or select your business logo. Leave empty to use site logo.', 'simple-invoice'); ?></p>
                            <?php if (!empty($settings['business_logo'])): ?>
                                <div class="si-logo-preview">
                                    <img src="<?php echo esc_url($settings['business_logo']); ?>" alt="<?php echo esc_attr__('Business Logo', 'simple-invoice'); ?>" style="max-width: 200px; height: auto;" />
                                </div>
                            <?php endif; ?>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="gstin"><?php echo esc_html__('GSTIN / Tax ID', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text" 
                                   id="gstin" 
                                   name="gstin" 
                                   value="<?php echo esc_attr($settings['gstin'] ?? ''); ?>" 
                                   class="regular-text" 
                                   placeholder="<?php echo esc_attr__('Enter your GSTIN or Tax ID', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your business tax identification number.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Invoice Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="invoice-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-media-text"></span>
                            <?php echo esc_html__('Invoice Settings', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Configure default settings for your invoices and billing preferences.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="default_due_days"><?php echo esc_html__('Default Due Days', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="number" 
                                   id="default_due_days" 
                                   name="default_due_days" 
                                   value="<?php echo esc_attr($settings['default_due_days'] ?? 7); ?>" 
                                   class="small-text" 
                                   min="1" 
                                   max="365" />
                            <p class="description"><?php echo esc_html__('Number of days after invoice date when payment is due.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Payment Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="payment-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-money-alt"></span>
                            <?php echo esc_html__('Payment Settings', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Set up your payment methods and banking information for client payments.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row"><?php echo esc_html__('Accepted Payment Methods', 'simple-invoice'); ?></th>
                        <td>
                            <fieldset>
                                <legend class="screen-reader-text"><?php echo esc_html__('Payment Methods', 'simple-invoice'); ?></legend>
                                
                                <label>
                                    <input type="checkbox"
                                           id="bank_enabled"
                                           name="payment_methods[]"
                                           value="bank"
                                           <?php checked(in_array('bank', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('Bank Transfer', 'simple-invoice'); ?>
                                </label><br />

                                <label>
                                    <input type="checkbox"
                                           id="paypal_enabled"
                                           name="payment_methods[]"
                                           value="paypal"
                                           <?php checked(in_array('paypal', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('PayPal', 'simple-invoice'); ?>
                                </label><br />

                                <label>
                                    <input type="checkbox"
                                           id="upi_enabled"
                                           name="payment_methods[]"
                                           value="upi"
                                           <?php checked(in_array('upi', $settings['payment_methods'] ?? array())); ?> />
                                    <?php echo esc_html__('UPI Payment', 'simple-invoice'); ?>
                                </label>
                            </fieldset>
                            <p class="description"><?php echo esc_html__('Select the payment methods you accept.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr id="bank_details_row" style="display: <?php echo in_array('bank', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="bank_details"><?php echo esc_html__('Bank Details', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="bank_details"
                                      name="bank_details"
                                      rows="4"
                                      class="large-text"
                                      placeholder="<?php echo esc_attr__('Bank Name: XYZ Bank\nAccount Number: **********\nIFSC Code: ABCD0123456\nAccount Holder: Your Business Name', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['bank_details'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Your bank account details for wire transfers.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr id="paypal_email_row" style="display: <?php echo in_array('paypal', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="paypal_email"><?php echo esc_html__('PayPal Email', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="email"
                                   id="paypal_email"
                                   name="paypal_email"
                                   value="<?php echo esc_attr($settings['paypal_email'] ?? ''); ?>"
                                   class="regular-text"
                                   placeholder="<?php echo esc_attr__('<EMAIL>', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your PayPal email address for receiving payments.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>

                    <tr id="upi_id_row" style="display: <?php echo in_array('upi', $settings['payment_methods'] ?? array()) ? 'table-row' : 'none'; ?>;">
                        <th scope="row">
                            <label for="upi_id"><?php echo esc_html__('UPI ID', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <input type="text"
                                   id="upi_id"
                                   name="upi_id"
                                   value="<?php echo esc_attr($settings['upi_id'] ?? ''); ?>"
                                   class="regular-text"
                                   placeholder="<?php echo esc_attr__('yourname@paytm', 'simple-invoice'); ?>" />
                            <p class="description"><?php echo esc_html__('Your UPI ID for generating QR codes on invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
                </div>
            </div>

            <!-- Footer Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="footer-settings" style="display: none;">
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-editor-alignleft"></span>
                            <?php echo esc_html__('Footer & Terms', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Customize footer messages and terms & conditions for your invoices.', 'simple-invoice'); ?>
                        </p>
                    </div>
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="footer_notes"><?php echo esc_html__('Footer Notes', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="footer_notes" 
                                      name="footer_notes" 
                                      rows="3" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Thank you for your business!', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['footer_notes'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Thank you message or additional notes to display at the bottom of invoices.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">
                            <label for="terms_text"><?php echo esc_html__('Terms & Conditions', 'simple-invoice'); ?></label>
                        </th>
                        <td>
                            <textarea id="terms_text" 
                                      name="terms_text" 
                                      rows="4" 
                                      class="large-text" 
                                      placeholder="<?php echo esc_attr__('Payment is due within the specified due date. Late payments may incur additional charges.', 'simple-invoice'); ?>"><?php echo esc_textarea($settings['terms_text'] ?? ''); ?></textarea>
                            <p class="description"><?php echo esc_html__('Terms and conditions or refund policy text.', 'simple-invoice'); ?></p>
                        </td>
                    </tr>
                </table>
            </div>

            <!-- Plugin Settings Tab -->
            <div class="si-settings-tab si-tab-content" id="clear-data" style="display: none;">
                <div class="si-settings-section si-plugin-settings-section">
                    <div class="si-section-header">
                        <div class="si-section-title-wrapper">
                            <div class="si-section-icon">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2579 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="si-section-title-content">
                                <h2><?php echo esc_html__('Plugin Settings', 'simple-invoice'); ?></h2>
                                <p class="si-section-description">
                                    <?php echo esc_html__('Configure plugin behavior and data management options.', 'simple-invoice'); ?>
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="si-plugin-settings-content">
                        <div class="si-setting-card">
                            <div class="si-setting-header">
                                <div class="si-setting-icon">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="si-setting-info">
                                    <h3><?php echo esc_html__('Automatic Data Cleanup', 'simple-invoice'); ?></h3>
                                    <p><?php echo esc_html__('Control what happens to your data when the plugin is deactivated', 'simple-invoice'); ?></p>
                                </div>
                            </div>

                            <div class="si-setting-control">
                                <label class="si-toggle-switch">
                                    <input type="checkbox"
                                           name="clear_data_on_deactivation"
                                           value="1"
                                           <?php checked(1, $settings['clear_data_on_deactivation'] ?? 0); ?> />
                                    <span class="si-toggle-slider"></span>
                                    <span class="si-toggle-label">
                                        <?php echo esc_html__('Clear all plugin data when deactivating', 'simple-invoice'); ?>
                                    </span>
                                </label>
                            </div>

                            <div class="si-setting-warning">
                                <div class="si-warning-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    </svg>
                                </div>
                                <div class="si-warning-content">
                                    <strong><?php echo esc_html__('Important:', 'simple-invoice'); ?></strong>
                                    <?php echo esc_html__('When enabled, all clients, templates, invoices, and settings will be permanently deleted when you deactivate the plugin. This action cannot be undone.', 'simple-invoice'); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Clear Data Section -->
                <div class="si-settings-section">
                    <div class="si-section-header">
                        <h2>
                            <span class="dashicons dashicons-trash"></span>
                            <?php echo esc_html__('Clear Plugin Data', 'simple-invoice'); ?>
                        </h2>
                        <p class="si-section-description">
                            <?php echo esc_html__('Permanently delete specific types of data from the plugin. Use with caution as this action cannot be undone.', 'simple-invoice'); ?>
                        </p>
                    </div>

                    <!-- Data Overview -->
                    <div class="si-data-overview">
                        <h3><?php echo esc_html__('Current Data Overview', 'simple-invoice'); ?></h3>
                        <div class="si-data-stats">
                            <?php
                            $data_counts = si_get_data_counts();
                            $clients_count = $data_counts['clients'];
                            $templates_count = $data_counts['templates'];
                            $invoices_count = $data_counts['invoices'];
                            ?>
                            <div class="si-stat-item">
                                <span class="si-stat-number"><?php echo esc_html($clients_count); ?></span>
                                <span class="si-stat-label"><?php echo esc_html__('Clients', 'simple-invoice'); ?></span>
                            </div>
                            <div class="si-stat-item">
                                <span class="si-stat-number"><?php echo esc_html($templates_count); ?></span>
                                <span class="si-stat-label"><?php echo esc_html__('Templates', 'simple-invoice'); ?></span>
                            </div>
                            <div class="si-stat-item">
                                <span class="si-stat-number"><?php echo esc_html($invoices_count); ?></span>
                                <span class="si-stat-label"><?php echo esc_html__('Invoices', 'simple-invoice'); ?></span>
                            </div>
                        </div>
                    </div>

                    <!-- Clear Data Options -->
                    <div class="si-clear-options">
                        <h3><?php echo esc_html__('Select Data to Clear', 'simple-invoice'); ?></h3>
                        <p class="description">
                            <?php echo esc_html__('Choose which types of data you want to permanently delete. This action cannot be undone.', 'simple-invoice'); ?>
                        </p>

                        <div class="si-clear-checkboxes">
                            <div class="si-clear-option">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_clients" id="clear_clients" value="1" />
                                    <span class="si-checkbox-custom"></span>
                                    <div class="si-option-content">
                                        <strong><?php echo esc_html__('Clients', 'simple-invoice'); ?></strong>
                                        <span class="si-option-description">
                                            <?php echo sprintf(esc_html__('Delete all %d client records and their information', 'simple-invoice'), $clients_count); ?>
                                        </span>
                                    </div>
                                </label>
                            </div>

                            <div class="si-clear-option">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_templates" id="clear_templates" value="1" />
                                    <span class="si-checkbox-custom"></span>
                                    <div class="si-option-content">
                                        <strong><?php echo esc_html__('Templates', 'simple-invoice'); ?></strong>
                                        <span class="si-option-description">
                                            <?php echo sprintf(esc_html__('Delete all %d invoice templates and their configurations', 'simple-invoice'), $templates_count); ?>
                                        </span>
                                    </div>
                                </label>
                            </div>

                            <div class="si-clear-option">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_invoices" id="clear_invoices" value="1" />
                                    <span class="si-checkbox-custom"></span>
                                    <div class="si-option-content">
                                        <strong><?php echo esc_html__('Invoices', 'simple-invoice'); ?></strong>
                                        <span class="si-option-description">
                                            <?php echo sprintf(esc_html__('Delete all %d generated invoices and their data', 'simple-invoice'), $invoices_count); ?>
                                        </span>
                                    </div>
                                </label>
                            </div>

                            <div class="si-clear-option">
                                <label class="si-clear-label">
                                    <input type="checkbox" name="clear_settings" id="clear_settings" value="1" />
                                    <span class="si-checkbox-custom"></span>
                                    <div class="si-option-content">
                                        <strong><?php echo esc_html__('Plugin Settings', 'simple-invoice'); ?></strong>
                                        <span class="si-option-description">
                                            <?php echo esc_html__('Reset all plugin settings to default values', 'simple-invoice'); ?>
                                        </span>
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="si-clear-actions">
                        <div class="si-clear-warning">
                            <span class="dashicons dashicons-warning"></span>
                            <strong><?php echo esc_html__('Warning:', 'simple-invoice'); ?></strong>
                            <?php echo esc_html__('This action is permanent and cannot be undone. Make sure you have a backup if needed.', 'simple-invoice'); ?>
                        </div>

                        <div class="si-clear-buttons">
                            <button type="button" class="button" id="si-select-all-data">
                                <?php echo esc_html__('Select All', 'simple-invoice'); ?>
                            </button>
                            <button type="button" class="button" id="si-deselect-all-data">
                                <?php echo esc_html__('Deselect All', 'simple-invoice'); ?>
                            </button>
                            <button type="submit" name="clear_data_submit" class="button button-secondary" id="si-clear-data-btn" disabled>
                                <span class="dashicons dashicons-trash"></span>
                                <?php echo esc_html__('Clear Selected Data', 'simple-invoice'); ?>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

        </div>

        <!-- Save Button (Fixed Position) - Only for main settings -->
        <div class="si-settings-save" id="si-main-save-button">
            <input type="submit" name="save_settings" id="si-save-settings" class="button button-primary" value="<?php echo esc_attr__('Save Settings', 'simple-invoice'); ?>" />
            <input type="submit" name="debug_save" class="button button-secondary" value="Debug Save" style="margin-left: 10px;" />
            <span class="si-save-status"></span>
        </div>
    </form>

    <!-- Clear Data Form - Separate form for clear data functionality -->
    <form method="post" action="" class="si-clear-data-form" id="si-clear-data-form" style="display: none;">
        <?php wp_nonce_field('si_clear_data_nonce'); ?>
        <input type="hidden" name="clear_clients" id="hidden_clear_clients" value="" />
        <input type="hidden" name="clear_templates" id="hidden_clear_templates" value="" />
        <input type="hidden" name="clear_invoices" id="hidden_clear_invoices" value="" />
        <input type="hidden" name="clear_settings" id="hidden_clear_settings" value="" />
    </form>
</div>

<style>
/* Settings Page Styling */
.si-settings-wrap {
    background: #ffffff;
    margin: 0 -20px;
    padding: 20px;
    min-height: 100vh;
}

.wp-heading-inline {
    color: #000000;
    font-size: 28px;
    font-weight: 700;
}

.page-title-action {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff !important;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.page-title-action:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    color: #ffffff !important;
    text-decoration: none;
    transform: translateY(-1px);
}

/* Enhanced Tab Styling */
.si-settings-nav {
    margin: 20px 0;
    border-bottom: 2px solid #e7e7e7;
}

.nav-tab-wrapper {
    border-bottom: none;
    margin: 0;
    padding: 0;
}

.nav-tab {
    background: #e7e7e7;
    color: #5f5f5f;
    border: 1px solid #e7e7e7;
    border-bottom: none;
    padding: 12px 20px;
    margin: 0 2px 0 0;
    border-radius: 8px 8px 0 0;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-tab:hover {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    text-decoration: none;
    transform: translateY(-2px);
}

.nav-tab-active,
.nav-tab-active:hover {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border-color: #f47a45;
    transform: translateY(0);
}

.nav-tab-active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: #f47a45;
}

.nav-tab .dashicons {
    font-size: 16px;
    width: 16px;
    height: 16px;
}

/* Settings Container */
.si-settings-container {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 0 8px 8px 8px;
    padding: 0;
    margin-top: -1px;
}

.si-settings-section {
    padding: 30px;
}

.si-section-header {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e7e7e7;
}

.si-section-header h2 {
    color: #000000;
    font-size: 22px;
    font-weight: 700;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.si-section-header .dashicons {
    color: #f47a45;
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.si-section-description {
    color: #5f5f5f;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

/* Form Table Styling */
.form-table th {
    color: #000000;
    font-weight: 600;
    padding: 15px 10px 15px 0;
}

.form-table td {
    padding: 15px 10px;
}

.form-table input[type="text"],
.form-table input[type="email"],
.form-table input[type="url"],
.form-table input[type="number"],
.form-table textarea {
    border: 2px solid #e7e7e7;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-table input[type="text"]:focus,
.form-table input[type="email"]:focus,
.form-table input[type="url"]:focus,
.form-table input[type="number"]:focus,
.form-table textarea:focus {
    border-color: #f47a45;
    box-shadow: 0 0 0 1px #f47a45;
    outline: none;
}

.description {
    color: #5f5f5f;
    font-style: italic;
}

/* Save Button */
.si-settings-save {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #ffffff;
    padding: 15px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border: 1px solid #e7e7e7;
    z-index: 1000;
}

#si-save-settings {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto !important;
    z-index: 1001;
    position: relative;
}

#si-save-settings:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    transform: translateY(-2px);
}

#si-save-settings:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Plugin Settings Section */
.si-plugin-settings-section {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 12px;
    margin-bottom: 20px;
}

.si-section-title-wrapper {
    display: flex;
    align-items: center;
    gap: 15px;
}

.si-section-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.si-setting-card {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 15px;
}

.si-setting-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.si-setting-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
}

.si-setting-info h3 {
    color: #000000;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.si-setting-info p {
    color: #5f5f5f;
    font-size: 14px;
    margin: 0;
}

/* Toggle Switch */
.si-toggle-switch {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
}

.si-toggle-switch input[type="checkbox"] {
    display: none;
}

.si-toggle-slider {
    width: 50px;
    height: 26px;
    background: #e7e7e7;
    border-radius: 13px;
    position: relative;
    transition: background 0.3s ease;
}

.si-toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.si-toggle-switch input[type="checkbox"]:checked + .si-toggle-slider {
    background: #f47a45;
}

.si-toggle-switch input[type="checkbox"]:checked + .si-toggle-slider::before {
    transform: translateX(24px);
}

.si-toggle-label {
    color: #000000;
    font-weight: 500;
}

/* Warning Box */
.si-setting-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 12px;
    margin-top: 15px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.si-warning-icon {
    color: #f47a45;
    flex-shrink: 0;
    margin-top: 2px;
}

.si-warning-content {
    color: #5f5f5f;
    font-size: 14px;
    line-height: 1.4;
}

.si-warning-content strong {
    color: #000000;
}

/* Plugin Settings Save Button */
.si-plugin-settings-save {
    padding: 20px 30px;
    border-top: 1px solid #e7e7e7;
    background: #ffffff;
    border-radius: 0 0 12px 12px;
}

.si-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.si-btn-primary {
    background: linear-gradient(135deg, #f47a45 0%, #5f5f5f 100%);
    color: #ffffff;
}

.si-btn-primary:hover {
    background: linear-gradient(135deg, #5f5f5f 0%, #000000 100%);
    transform: translateY(-2px);
}

/* Data Overview */
.si-data-overview {
    background: #ffffff;
    border: 1px solid #e7e7e7;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.si-data-overview h3 {
    color: #000000;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.si-data-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.si-data-stats .si-stat-item {
    background: #e7e7e7;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}

.si-data-stats .si-stat-number {
    font-size: 24px;
    font-weight: 700;
    color: #f47a45;
    display: block;
    margin-bottom: 5px;
}

.si-data-stats .si-stat-label {
    font-size: 12px;
    color: #5f5f5f;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .si-settings-save {
        position: static;
        margin-top: 20px;
        box-shadow: none;
        border: 1px solid #e7e7e7;
    }

    .nav-tab {
        padding: 10px 15px;
        font-size: 13px;
    }

    .si-section-header h2 {
        font-size: 20px;
    }

    .si-setting-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    // Tab functionality
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();

        var targetTab = $(this).data('tab');

        // Remove active class from all tabs
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        // Hide all tab content
        $('.si-tab-content').hide();

        // Show target tab content
        $('#' + targetTab).show();

        // Show/hide appropriate save buttons
        if (targetTab === 'clear-data') {
            $('#si-main-save-button').hide();
        } else {
            $('#si-main-save-button').show();
        }

        // Update URL hash
        window.location.hash = targetTab;
    });

    // Check for hash on page load
    if (window.location.hash) {
        var hash = window.location.hash.substring(1);
        var targetTab = $('.nav-tab[data-tab="' + hash + '"]');
        if (targetTab.length) {
            targetTab.trigger('click');
        }
    }

    // Initialize save button visibility
    var currentTab = $('.nav-tab-active').data('tab');
    if (currentTab === 'clear-data') {
        $('#si-main-save-button').hide();
    } else {
        $('#si-main-save-button').show();
    }

    // Media uploader for logo
    $('.si-media-button').on('click', function(e) {
        e.preventDefault();

        var button = $(this);
        var targetInput = $('#' + button.data('target'));

        var mediaUploader = wp.media({
            title: '<?php echo esc_js(__('Select Business Logo', 'simple-invoice')); ?>',
            button: {
                text: '<?php echo esc_js(__('Use this image', 'simple-invoice')); ?>'
            },
            multiple: false
        });

        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            targetInput.val(attachment.url);

            // Update preview if exists
            var preview = targetInput.closest('td').find('.si-logo-preview');
            if (preview.length) {
                preview.find('img').attr('src', attachment.url);
            } else {
                targetInput.after('<div class="si-logo-preview"><img src="' + attachment.url + '" alt="<?php echo esc_attr__('Business Logo', 'simple-invoice'); ?>" style="max-width: 200px; height: auto; margin-top: 10px;" /></div>');
            }
        });

        mediaUploader.open();
    });

    // Payment method field toggles
    function initPaymentToggles() {
        console.log('Initializing payment toggles...');

        // Bank Transfer toggle
        $('#bank_enabled').on('change', function() {
            console.log('Bank checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#bank_details_row').show().fadeIn(300);
            } else {
                $('#bank_details_row').fadeOut(300);
            }
        });

        // PayPal toggle
        $('#paypal_enabled').on('change', function() {
            console.log('PayPal checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#paypal_email_row').show().fadeIn(300);
            } else {
                $('#paypal_email_row').fadeOut(300);
            }
        });

        // UPI toggle
        $('#upi_enabled').on('change', function() {
            console.log('UPI checkbox changed:', $(this).is(':checked'));
            if ($(this).is(':checked')) {
                $('#upi_id_row').show().fadeIn(300);
            } else {
                $('#upi_id_row').fadeOut(300);
            }
        });

        console.log('Payment toggles initialized');
    }

    // Initialize payment toggles
    initPaymentToggles();

    // Force form submission when save button is clicked
    $('#si-save-settings').on('click', function(e) {
        e.preventDefault(); // Prevent default to handle manually
        console.log('Save button clicked - forcing form submission');

        // Get the form and submit it
        var $form = $('.si-settings-form');
        console.log('Form found:', $form.length);

        if ($form.length) {
            console.log('Submitting form...');
            // Now that we fixed the name conflict, native submit should work
            $form[0].submit();
        } else {
            console.error('Form not found!');
        }
    });

    // Auto-save indication
    $('.si-settings-form input, .si-settings-form textarea, .si-settings-form select').on('change', function() {
        $('.si-save-status').text('<?php echo esc_js(__('Unsaved changes', 'simple-invoice')); ?>').removeClass('success').addClass('warning');
    });

    // Clear Data Tab Functionality
    function updateClearButton() {
        var anyChecked = $('.si-clear-checkboxes input[type="checkbox"]:checked').length > 0;
        $('#si-clear-data-btn').prop('disabled', !anyChecked);
    }

    // Monitor checkbox changes
    $('.si-clear-checkboxes input[type="checkbox"]').on('change', function() {
        updateClearButton();
    });

    // Select all data
    $('#si-select-all-data').on('click', function() {
        $('.si-clear-checkboxes input[type="checkbox"]').prop('checked', true);
        updateClearButton();
    });

    // Deselect all data
    $('#si-deselect-all-data').on('click', function() {
        $('.si-clear-checkboxes input[type="checkbox"]').prop('checked', false);
        updateClearButton();
    });

    // Clear data button click handler
    $('#si-clear-data-btn').on('click', function(e) {
        e.preventDefault();

        var checkedItems = $('.si-clear-checkboxes input[type="checkbox"]:checked');

        if (checkedItems.length === 0) {
            alert('<?php echo esc_js(__('Please select at least one data type to clear.', 'simple-invoice')); ?>');
            return false;
        }

        var itemNames = [];
        checkedItems.each(function() {
            var label = $(this).closest('.si-clear-option').find('strong').text();
            itemNames.push(label);
        });

        var confirmMessage = '<?php echo esc_js(__('Are you sure you want to permanently delete the following data?', 'simple-invoice')); ?>\n\n';
        confirmMessage += '• ' + itemNames.join('\n• ') + '\n\n';
        confirmMessage += '<?php echo esc_js(__('This action cannot be undone. Do you want to continue?', 'simple-invoice')); ?>';

        if (!confirm(confirmMessage)) {
            return false;
        }

        // Copy checked values to hidden form and submit
        checkedItems.each(function() {
            var name = $(this).attr('name');
            var hiddenInput = $('#hidden_' + name);
            if (hiddenInput.length) {
                hiddenInput.val($(this).val());
            }
        });

        // Submit the hidden form
        $('#si-clear-data-form').submit();
    });

    // Show notification for clear data operations
    function showClearDataNotification(message, type) {
        // Remove existing notifications
        $('.si-clear-notification').remove();

        var notificationClass = type === 'success' ? 'notice-success' : 'notice-error';
        var notification = $('<div class="notice ' + notificationClass + ' si-clear-notification is-dismissible"><p>' + message + '</p></div>');

        // Insert after the clear data form
        $('.si-clear-data-form').before(notification);

        // Auto-hide after 5 seconds
        setTimeout(function() {
            notification.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);

        // Scroll to notification
        $('html, body').animate({
            scrollTop: notification.offset().top - 100
        }, 500);
    }

    // Initialize clear button state
    updateClearButton();
});
</script>
