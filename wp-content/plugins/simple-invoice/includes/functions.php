<?php
/**
 * Helper Functions for Simple Invoice Plugin
 *
 * @package SimpleInvoice
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Get plugin settings
 *
 * @param string $key Optional. Specific setting key to retrieve
 * @return mixed Settings array or specific setting value
 * @since 1.0.0
 */
function si_get_settings($key = '') {
    $settings = get_option('si_settings', array());
    
    if (!empty($key)) {
        return isset($settings[$key]) ? $settings[$key] : '';
    }
    
    return $settings;
}

/**
 * Update plugin settings
 *
 * @param array $new_settings New settings to save
 * @return bool True on success, false on failure
 * @since 1.0.0
 */
function si_update_settings($new_settings) {
    error_log('si_update_settings called with: ' . print_r($new_settings, true));

    $current_settings = si_get_settings();
    error_log('Current settings: ' . print_r($current_settings, true));

    $updated_settings = wp_parse_args($new_settings, $current_settings);
    error_log('Final settings to save: ' . print_r($updated_settings, true));

    // Check if settings actually changed
    $settings_changed = ($current_settings !== $updated_settings);
    error_log('Settings changed: ' . ($settings_changed ? 'yes' : 'no'));

    // Force update even if no change detected
    $result = update_option('si_settings', $updated_settings, true);
    error_log('update_option result: ' . var_export($result, true));

    // If update_option returned false, try delete and add
    if (!$result && $settings_changed) {
        error_log('update_option failed, trying delete and add approach');
        delete_option('si_settings');
        $result = add_option('si_settings', $updated_settings);
        error_log('delete and add_option result: ' . var_export($result, true));
    }

    // Verify the save worked
    $saved_settings = get_option('si_settings');
    error_log('Settings after save verification: ' . print_r($saved_settings, true));

    // Check if the saved settings match what we wanted to save
    $save_successful = ($saved_settings === $updated_settings);
    error_log('Save verification successful: ' . ($save_successful ? 'yes' : 'no'));

    // If strict comparison fails, check if the important data is there
    if (!$save_successful) {
        error_log('Strict comparison failed, checking individual fields...');
        $important_fields_match = true;
        foreach ($new_settings as $key => $value) {
            if (!isset($saved_settings[$key]) || $saved_settings[$key] !== $value) {
                error_log("Field mismatch - $key: expected " . print_r($value, true) . ", got " . print_r($saved_settings[$key] ?? 'NOT_SET', true));
                $important_fields_match = false;
                break;
            }
        }
        error_log('Important fields match: ' . ($important_fields_match ? 'yes' : 'no'));
        $save_successful = $important_fields_match;
    }

    return $save_successful;
}

/**
 * Sanitize text input
 *
 * @param string $input Input to sanitize
 * @return string Sanitized input
 * @since 1.0.0
 */
function si_sanitize_text($input) {
    return sanitize_text_field(trim($input));
}

/**
 * Sanitize textarea input
 *
 * @param string $input Input to sanitize
 * @return string Sanitized input
 * @since 1.0.0
 */
function si_sanitize_textarea($input) {
    return sanitize_textarea_field(trim($input));
}

/**
 * Sanitize email input
 *
 * @param string $input Input to sanitize
 * @return string Sanitized email
 * @since 1.0.0
 */
function si_sanitize_email($input) {
    return sanitize_email(trim($input));
}

/**
 * Sanitize URL input
 *
 * @param string $input Input to sanitize
 * @return string Sanitized URL
 * @since 1.0.0
 */
function si_sanitize_url($input) {
    return esc_url_raw(trim($input));
}

/**
 * Generate unique invoice number
 *
 * @return string Unique invoice number
 * @since 1.0.0
 */
function si_generate_invoice_number() {
    $prefix = 'INV-';
    $date = date('Y-m-d');
    $random = wp_rand(1000, 9999);
    
    return $prefix . $date . '-' . $random;
}

/**
 * Format currency amount
 *
 * @param float $amount Amount to format
 * @param string $currency Currency symbol
 * @return string Formatted amount
 * @since 1.0.0
 */
function si_format_currency($amount, $currency = '₹') {
    return $currency . number_format((float)$amount, 2);
}

/**
 * Get available designs
 *
 * @return array Available design options
 * @since 1.0.0
 */
function si_get_available_designs() {
    $designs_path = SI_PLUGIN_PATH . 'designs/';
    $designs = array();
    
    if (is_dir($designs_path)) {
        $design_dirs = scandir($designs_path);
        
        foreach ($design_dirs as $dir) {
            if ($dir !== '.' && $dir !== '..' && is_dir($designs_path . $dir)) {
                $template_file = $designs_path . $dir . '/template.php';
                $preview_file = $designs_path . $dir . '/preview.jpg';
                
                if (file_exists($template_file)) {
                    $designs[$dir] = array(
                        'name' => ucfirst($dir),
                        'path' => $template_file,
                        'preview' => file_exists($preview_file) ? SI_PLUGIN_URL . 'designs/' . $dir . '/preview.jpg' : ''
                    );
                }
            }
        }
    }
    
    return $designs;
}

/**
 * Verify nonce for AJAX requests
 *
 * @param string $action Action name
 * @param string $nonce_field Nonce field name
 * @return bool True if valid, false otherwise
 * @since 1.0.0
 */
function si_verify_ajax_nonce($action, $nonce_field = 'nonce') {
    if (!isset($_POST[$nonce_field])) {
        return false;
    }
    
    return wp_verify_nonce($_POST[$nonce_field], $action);
}

/**
 * Send JSON response for AJAX requests
 *
 * @param bool $success Success status
 * @param string $message Response message
 * @param array $data Additional data
 * @since 1.0.0
 */
function si_send_json_response($success, $message, $data = array()) {
    wp_send_json(array(
        'success' => $success,
        'message' => $message,
        'data' => $data
    ));
}

/**
 * Get client by ID
 *
 * @param int $client_id Client ID
 * @return object|null Client object or null if not found
 * @since 1.0.0
 */
function si_get_client($client_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'si_clients';
    
    return $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $client_id
        )
    );
}

/**
 * Get all clients
 *
 * @param array $args Query arguments
 * @return array Array of client objects
 * @since 1.0.0
 */
function si_get_clients($args = array()) {
    global $wpdb;
    
    $defaults = array(
        'orderby' => 'name',
        'order' => 'ASC',
        'limit' => -1,
        'offset' => 0
    );
    
    $args = wp_parse_args($args, $defaults);
    $table_name = $wpdb->prefix . 'si_clients';
    
    $sql = "SELECT * FROM $table_name";
    
    // Add ORDER BY clause
    $sql .= " ORDER BY " . esc_sql($args['orderby']) . " " . esc_sql($args['order']);
    
    // Add LIMIT clause
    if ($args['limit'] > 0) {
        $sql .= " LIMIT " . intval($args['offset']) . ", " . intval($args['limit']);
    }
    
    return $wpdb->get_results($sql);
}

/**
 * Get template by ID
 *
 * @param int $template_id Template ID
 * @return object|null Template object or null if not found
 * @since 1.0.0
 */
function si_get_template($template_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'si_templates';
    
    return $wpdb->get_row(
        $wpdb->prepare(
            "SELECT * FROM $table_name WHERE id = %d",
            $template_id
        )
    );
}

/**
 * Get all templates
 *
 * @param array $args Query arguments
 * @return array Array of template objects
 * @since 1.0.0
 */
function si_get_templates($args = array()) {
    global $wpdb;
    
    $defaults = array(
        'orderby' => 'name',
        'order' => 'ASC',
        'limit' => -1,
        'offset' => 0
    );
    
    $args = wp_parse_args($args, $defaults);
    $table_name = $wpdb->prefix . 'si_templates';
    
    $sql = "SELECT * FROM $table_name";
    
    // Add ORDER BY clause
    $sql .= " ORDER BY " . esc_sql($args['orderby']) . " " . esc_sql($args['order']);
    
    // Add LIMIT clause
    if ($args['limit'] > 0) {
        $sql .= " LIMIT " . intval($args['offset']) . ", " . intval($args['limit']);
    }
    
    return $wpdb->get_results($sql);
}

/**
 * Generate UPI QR code data
 *
 * @param string $upi_id UPI ID
 * @param float $amount Amount
 * @param string $note Payment note
 * @return string UPI payment string
 * @since 1.0.0
 */
function si_generate_upi_string($upi_id, $amount = 0, $note = '') {
    $upi_string = "upi://pay?pa=" . urlencode($upi_id);
    
    if ($amount > 0) {
        $upi_string .= "&am=" . number_format($amount, 2, '.', '');
    }
    
    if (!empty($note)) {
        $upi_string .= "&tn=" . urlencode($note);
    }
    
    return $upi_string;
}

/**
 * Log debug information
 *
 * @param mixed $data Data to log
 * @param string $title Log title
 * @since 1.0.0
 */
function si_debug_log($data, $title = 'Simple Invoice Debug') {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log($title . ': ' . print_r($data, true));
    }
}

/**
 * Clear plugin data based on selected options
 *
 * @param array $clear_options Array of data types to clear
 * @return bool True on success, false on failure
 * @since 1.0.0
 */
function si_clear_plugin_data($clear_options = array()) {
    global $wpdb;

    error_log('Simple Invoice: si_clear_plugin_data called with options: ' . print_r($clear_options, true));

    if (empty($clear_options)) {
        error_log('Simple Invoice: No clear options provided');
        return false;
    }

    $success = true;

    try {
        // Clear clients data
        if (in_array('clients', $clear_options)) {
            error_log('Simple Invoice: Attempting to clear clients data');
            $result = $wpdb->query("DELETE FROM {$wpdb->prefix}si_clients");
            error_log('Simple Invoice: Clients delete query result: ' . var_export($result, true));
            if ($result === false) {
                $success = false;
                error_log('Simple Invoice: Failed to clear clients data - ' . $wpdb->last_error);
            } else {
                error_log('Simple Invoice: Cleared clients data - ' . $result . ' records deleted');
            }
        }

        // Clear templates data
        if (in_array('templates', $clear_options)) {
            error_log('Simple Invoice: Attempting to clear templates data');
            $result = $wpdb->query("DELETE FROM {$wpdb->prefix}si_templates");
            error_log('Simple Invoice: Templates delete query result: ' . var_export($result, true));
            if ($result === false) {
                $success = false;
                error_log('Simple Invoice: Failed to clear templates data - ' . $wpdb->last_error);
            } else {
                error_log('Simple Invoice: Cleared templates data - ' . $result . ' records deleted');
            }
        }

        // Clear invoices data
        if (in_array('invoices', $clear_options)) {
            error_log('Simple Invoice: Attempting to clear invoices data');
            $result = $wpdb->query("DELETE FROM {$wpdb->prefix}si_invoices");
            error_log('Simple Invoice: Invoices delete query result: ' . var_export($result, true));
            if ($result === false) {
                $success = false;
                error_log('Simple Invoice: Failed to clear invoices data - ' . $wpdb->last_error);
            } else {
                error_log('Simple Invoice: Cleared invoices data - ' . $result . ' records deleted');
            }
        }

        // Clear settings
        if (in_array('settings', $clear_options)) {
            error_log('Simple Invoice: Attempting to clear settings data');
            $result = delete_option('si_settings');
            error_log('Simple Invoice: Settings delete result: ' . var_export($result, true));
            if (!$result && get_option('si_settings') !== false) {
                $success = false;
                error_log('Simple Invoice: Failed to clear settings data');
            } else {
                error_log('Simple Invoice: Cleared settings data');
            }
        }

    } catch (Exception $e) {
        error_log('Simple Invoice: Error clearing data - ' . $e->getMessage());
        $success = false;
    }

    error_log('Simple Invoice: si_clear_plugin_data returning: ' . ($success ? 'true' : 'false'));
    return $success;
}

/**
 * Clear all plugin data (used on deactivation if enabled)
 *
 * @since 1.0.0
 */
function si_clear_all_plugin_data() {
    global $wpdb;

    try {
        // Get current settings to check if clear on deactivation is enabled
        $settings = si_get_settings();

        if (!isset($settings['clear_data_on_deactivation']) || !$settings['clear_data_on_deactivation']) {
            return; // Don't clear if not enabled
        }

        // Clear all tables
        $tables = array(
            $wpdb->prefix . 'si_clients',
            $wpdb->prefix . 'si_templates',
            $wpdb->prefix . 'si_invoices'
        );

        $total_deleted = 0;
        foreach ($tables as $table) {
            $result = $wpdb->query("DELETE FROM {$table}");
            if ($result !== false) {
                $total_deleted += $result;
            }
        }

        // Clear settings
        delete_option('si_settings');

        // Log the action
        error_log('Simple Invoice: All plugin data cleared on deactivation - ' . $total_deleted . ' total records deleted');

    } catch (Exception $e) {
        error_log('Simple Invoice: Error clearing data on deactivation - ' . $e->getMessage());
    }
}

/**
 * Drop all plugin tables (used on uninstall)
 *
 * @since 1.0.0
 */
function si_drop_plugin_tables() {
    global $wpdb;

    try {
        $tables = array(
            $wpdb->prefix . 'si_clients',
            $wpdb->prefix . 'si_templates',
            $wpdb->prefix . 'si_invoices'
        );

        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS {$table}");
        }

        // Clear settings
        delete_option('si_settings');

        error_log('Simple Invoice: All plugin tables dropped on uninstall');

    } catch (Exception $e) {
        error_log('Simple Invoice: Error dropping tables on uninstall - ' . $e->getMessage());
    }
}

/**
 * Get data counts for overview
 *
 * @return array Array of data counts
 * @since 1.0.0
 */
function si_get_data_counts() {
    global $wpdb;

    $counts = array(
        'clients' => 0,
        'templates' => 0,
        'invoices' => 0
    );

    try {
        $counts['clients'] = (int) $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}si_clients");
        $counts['templates'] = (int) $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}si_templates");
        $counts['invoices'] = (int) $wpdb->get_var("SELECT COUNT(*) FROM {$wpdb->prefix}si_invoices");
    } catch (Exception $e) {
        error_log('Simple Invoice: Error getting data counts - ' . $e->getMessage());
    }

    return $counts;
}
